# AI任务处理与代码清理综合术语表

## 🎯 核心概念定义

### [TERM-001] 绝对控制原则
**术语ID**：TERM-001  
**英文名称**：Absolute Control Principle  
**定义**：AI任务处理中的最高指导原则，要求所有关键决策必须获得用户确认，严禁AI自作主张  
**系统应用**：作为所有任务处理模式的基础约束，确保用户对任务执行的完全控制权  
**核心要求**：任务完成前必须请求最终确认，需求不明确时必须主动询问澄清  
**相关术语**：[TERM-002] 任务复杂度评估, [TERM-010] 执行检查清单, [TERM-017] 强制询问机制

### [TERM-002] 任务复杂度评估
**术语ID**：TERM-002  
**英文名称**：Task Complexity Assessment  
**定义**：根据任务特征对任务难度进行分级的评估体系，分为Level 1-5五个等级  
**系统应用**：决定采用何种执行模式，合理分配资源和时间  
**评估维度**：执行时间、文件修改数量、影响范围、风险程度  
**相关术语**：[TERM-003] ATOMIC-TASK模式, [TERM-004] LITE-CYCLE模式, [TERM-005] FULL-CYCLE模式

### [TERM-003] 代码清理专家
**术语ID**：TERM-003  
**英文名称**：Code Cleanup Specialist  
**定义**：负责在每次代码修改后自动执行清理检查的专门角色，确保代码库的整洁性和可维护性  
**系统应用**：作为代码修改后的强制执行步骤，维护代码质量  
**执行原则**：宁可保留，不可误删；分批执行大型清理；记录所有清理操作  
**相关术语**：[TERM-011] 自动清理项, [TERM-012] 需要确认项

## 🔄 任务处理模式

### [TERM-004] ATOMIC-TASK模式
**术语ID**：TERM-004  
**英文名称**：ATOMIC-TASK Mode  
**定义**：适用于Level 1任务的执行模式，处理单个明确修改的原子任务  
**适用标准**：预计执行时间<10分钟，风险低，影响范围小  
**执行流程**：分析任务→确认方案→执行→确认完成  
**相关术语**：[TERM-002] 任务复杂度评估, [TERM-005] LITE-CYCLE模式

### [TERM-005] LITE-CYCLE模式
**术语ID**：TERM-005  
**英文名称**：LITE-CYCLE Mode  
**定义**：适用于Level 2任务的执行模式，处理完整功能实现的标准任务  
**适用标准**：预计执行时间10-30分钟，涉及少量文件修改  
**执行流程**：生成计划→确认计划→批量执行→确认完成  
**相关术语**：[TERM-004] ATOMIC-TASK模式, [TERM-006] FULL-CYCLE模式

### [TERM-006] FULL-CYCLE模式
**术语ID**：TERM-006  
**英文名称**：FULL-CYCLE Mode  
**定义**：适用于Level 3任务的执行模式，处理大型重构或新模块的复杂任务  
**适用标准**：预计执行时间30分钟-2小时，需要深入研究  
**执行流程**：需求沟通→研究→方案权衡→规划→确认→执行→确认完成  
**相关术语**：[TERM-005] LITE-CYCLE模式, [TERM-007] COLLABORATIVE-ITERATION模式

### [TERM-007] COLLABORATIVE-ITERATION模式
**术语ID**：TERM-007  
**英文名称**：COLLABORATIVE-ITERATION Mode  
**定义**：适用于Level 4任务的执行模式，处理需求不明朗的探索任务  
**适用标准**：需求不明确，开放式问题，需要多轮探索  
**执行流程**：循环：提出想法→获取反馈→深入分析→确认进展  
**相关术语**：[TERM-006] FULL-CYCLE模式, [TERM-008] MEGA-TASK模式

### [TERM-008] MEGA-TASK模式
**术语ID**：TERM-008  
**英文名称**：MEGA-TASK Mode  
**定义**：适用于Level 5任务的执行模式，处理超大型任务  
**适用标准**：预计修改5个以上文件，涉及3个以上模块，需要跨会话完成  
**执行阶段**：初始化→分阶段执行→整合交付  
**相关术语**：[TERM-007] COLLABORATIVE-ITERATION模式, [TERM-009] 复杂度升级

## ⚡ 流程管理机制

### [TERM-009] 复杂度升级
**术语ID**：TERM-009  
**英文名称**：Complexity Escalation  
**定义**：当任务复杂度超出预期时，动态调整到更高级执行模式的机制  
**触发条件**：任务复杂度超出当前模式处理能力  
**处理流程**：声明升级建议→获取确认→切换到更高级模式  
**相关术语**：[TERM-002] 任务复杂度评估, [TERM-010] 偏离检测

### [TERM-010] 偏离检测
**术语ID**：TERM-010  
**英文名称**：Deviation Detection  
**定义**：监控任务执行过程中是否偏离原始目标的机制  
**检测标准**：当前工作与记录的核心目标不一致  
**纠正流程**：暂停→回顾目标→评估偏离→制定方案→确认调整  
**相关术语**：[TERM-001] 绝对控制原则, [TERM-011] 执行检查清单, [TERM-022] 对话结束控制

### [TERM-011] 执行检查清单
**术语ID**：TERM-011  
**英文名称**：Execution Checklist  
**定义**：确保任务执行质量的标准化检查项目列表  
**检查阶段**：任务开始前、执行过程中、任务完成前  
**核心内容**：复杂度评估、用户确认、进度跟踪、异常报告  
**相关术语**：[TERM-001] 绝对控制原则, [TERM-002] 任务复杂度评估, [TERM-021] 任务完成确认

## 🛡️ 代码质量管理

### [TERM-012] 自动清理项
**术语ID**：TERM-012  
**英文名称**：Automatic Cleanup Items  
**定义**：可以无需用户确认直接清理的明确无用代码项目  
**包含内容**：未使用的导入语句、重复导入、空函数、永假条件代码块、不可达代码  
**执行原则**：明确性、安全性、可逆性  
**相关术语**：[TERM-013] 需要确认项, [TERM-014] 代码引用关系分析

### [TERM-013] 需要确认项
**术语ID**：TERM-013  
**英文名称**：Confirmation Required Items  
**定义**：可能存在潜在用途，需要用户确认后才能清理的代码项目  
**包含内容**：公共API函数、动态调用代码、安全配置、预留代码  
**处理原则**：保守原则，用户决策，详细说明  
**相关术语**：[TERM-012] 自动清理项, [TERM-015] 保守原则

### [TERM-014] 代码引用关系分析
**术语ID**：TERM-014  
**英文名称**：Code Reference Analysis  
**定义**：通过分析代码间的引用关系来确定代码使用情况的技术手段  
**分析范围**：直接引用、间接引用、动态调用、字符串引用、反射调用  
**技术实现**：使用codebase-retrieval工具进行深度分析  
**相关术语**：[TERM-012] 自动清理项, [TERM-013] 需要确认项

### [TERM-015] 保守原则
**术语ID**：TERM-015  
**英文名称**：Conservative Principle  
**定义**：代码清理中"宁可保留，不可误删"的安全保障原则  
**应用场景**：不确定代码用途时、涉及核心业务逻辑时、影响范围不明时  
**实施策略**：分批执行、记录操作、可回滚设计  
**相关术语**：[TERM-013] 需要确认项, [TERM-016] 同步更新要求

### [TERM-016] 同步更新要求
**术语ID**：TERM-016  
**英文名称**：Synchronous Update Requirements  
**定义**：代码清理时必须同步更新相关文档和配置的要求  
**更新范围**：README.md、API文档、代码注释、配置文件说明  
**目的**：保持文档与代码的一致性，避免信息滞后  
**相关术语**：[TERM-003] 代码清理专家, [TERM-015] 保守原则

## 🔧 工具使用规范

### [TERM-017] 强制询问机制
**术语ID**：TERM-017
**英文名称**：Mandatory Inquiry Mechanism
**定义**：要求AI必须通过指定工具进行用户交互，禁止直接询问或自主决策的强制性机制
**系统应用**：确保所有用户交互都通过标准化工具进行，维护交互的一致性和可控性
**核心约束**：禁止直接询问、禁止自作主张、禁止未经确认的任务结束
**相关术语**：[TERM-001] 绝对控制原则, [TERM-018] 寸止工具, [TERM-021] 任务完成确认

### [TERM-018] 寸止工具
**术语ID**：TERM-018
**英文名称**：Cunzhi Tool (Pause-and-Ask Tool)
**定义**：MCP工具，用于AI与用户进行标准化交互，支持预定义选项和结构化询问
**系统应用**：作为AI与用户交互的唯一合法渠道，确保交互的规范性和可追踪性
**技术实现**：通过MCP协议调用，支持消息传递和选项提供
**相关术语**：[TERM-017] 强制询问机制, [TERM-019] 需求澄清询问, [TERM-020] 方案选择询问

### [TERM-019] 需求澄清询问
**术语ID**：TERM-019
**英文名称**：Requirement Clarification Inquiry
**定义**：当需求不明确时，通过寸止工具向用户询问澄清的标准化流程
**触发条件**：需求模糊、信息不足、理解存在歧义
**执行要求**：必须提供预定义选项，便于用户快速选择
**相关术语**：[TERM-018] 寸止工具, [TERM-020] 方案选择询问

### [TERM-020] 方案选择询问
**术语ID**：TERM-020
**英文名称**：Solution Selection Inquiry
**定义**：当存在多个可行方案时，通过寸止工具请求用户选择的机制
**适用场景**：多方案并存、策略需要更新、技术路线选择
**禁止行为**：AI自作主张选择方案或策略
**相关术语**：[TERM-018] 寸止工具, [TERM-019] 需求澄清询问, [TERM-021] 任务完成确认

### [TERM-021] 任务完成确认
**术语ID**：TERM-021
**英文名称**：Task Completion Confirmation
**定义**：任务即将完成前，必须通过寸止工具请求用户反馈和确认的强制性流程
**执行时机**：任务完成前的最后步骤
**确认内容**：任务完成质量、用户满意度、是否需要调整
**强制性**：绝对强制，未经确认不得结束任务
**相关术语**：[TERM-017] 强制询问机制, [TERM-018] 寸止工具, [TERM-022] 对话结束控制

### [TERM-022] 对话结束控制
**术语ID**：TERM-022
**英文名称**：Conversation Termination Control
**定义**：严格控制对话结束条件，禁止AI未经明确许可主动结束对话的机制
**控制条件**：必须通过寸止工具明确获得结束许可
**禁止行为**：主动结束对话、主动结束请求、未经确认的任务终止
**相关术语**：[TERM-021] 任务完成确认, [TERM-017] 强制询问机制

### [TERM-023] 记忆管理工具
**术语ID**：TERM-023
**英文名称**：Memory Management Tool
**定义**：用于存储和检索项目相关信息的MCP工具，支持规则、偏好、模式和上下文的管理
**系统应用**：维护项目记忆、保存用户偏好、记录重要变更
**技术实现**：通过MCP协议调用，支持添加和查询操作
**相关术语**：[TERM-024] 项目记忆查询, [TERM-025] 记忆分类管理

### [TERM-024] 项目记忆查询
**术语ID**：TERM-024
**英文名称**：Project Memory Query
**定义**：对话开始时自动查询项目相关记忆信息的标准化流程
**执行时机**：每次对话初始化时
**查询范围**：以git根目录为项目路径的所有相关记忆
**目的**：确保AI了解项目背景和历史决策
**相关术语**：[TERM-023] 记忆管理工具, [TERM-025] 记忆分类管理

### [TERM-025] 记忆分类管理
**术语ID**：TERM-025
**英文名称**：Memory Classification Management
**定义**：按照规则(rule)、偏好(preference)、模式(pattern)、上下文(context)对记忆进行分类管理的体系
**分类标准**：
- rule: 项目规则和约束
- preference: 用户偏好和习惯
- pattern: 最佳实践和模式
- context: 项目上下文信息
**更新原则**：仅在重要变更时更新，保持简洁
**相关术语**：[TERM-023] 记忆管理工具, [TERM-024] 项目记忆查询
